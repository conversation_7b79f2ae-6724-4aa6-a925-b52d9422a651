const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "";
import isArray from "lodash/isArray";
export const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
});

export const currencyFormatterRound = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 0,
  minimumFractionDigits: 0,
});

export const currencyFormatterOneDecimal = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 1,
  minimumFractionDigits: 0,
});

export const isHover = (e: HTMLElement) =>
  e?.parentElement?.querySelector(":hover") === e;

export const getSearchParams = (
  searchParams: Record<string, string | number>
) => {
  const params = [];
  for (const [key, value] of Object.entries(searchParams)) {
    if (isArray(value)) value.forEach((x) => params.push(key + "=" + x));
    else params.push(key + "=" + value);
  }
  const str = params.join("&");
  return str;
};

export const isJsonString = (str: string) => {
  try {
    const o = JSON.parse(str);
    if (o && typeof o === "object") {
      return true;
    }
  } catch (e) {
    return false;
  }
  return false;
};

export const aRandomString = (length = 10): string => {
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  return Array(length)
    .fill("")
    .reduce(
      (prev) =>
        prev + characters.charAt(Math.floor(Math.random() * characters.length)),
      ""
    );
};

export const checkIfDevelopment = () => BASE_URL.includes("dev");
